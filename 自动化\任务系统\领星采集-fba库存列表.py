# -*- coding:UTF-8 -*-
# @FileName  :领星采集-fba库存列表.py
# @Time      :2025/2/5 19:36
# <AUTHOR>

import schedule
import time
from datetime import datetime, timedelta
from utils_mrc.pub_fun import *
from work.自动化.领星抓取.DataFetcher import *
from work.自动化.领星抓取.LingXingSessionPage import validate_fba_inventory_data, repair_fba_inventory_data, init_lingxing_fba_inventory, insert_lingxing_data
from TimePinner import Pinner


def main_fetch_lingxing_data_v2(data_params):
    """使用DataFetcher采集库存数据"""
    finally_params = {  # 抓取参数
        'user': ['jszg01', 'yxyJS2'],
        'is_hide_zero_stock': 1,  # 隐藏0库存
    }
    save_params = {  # 入库参数
        'task_id': 1,
        'app_id': 1,
        'datetime': get_today_zero_timestamp(),
        'task_time': now_int()
    }
    finally_params.update(data_params)

    # 创建数据提供者
    provider = LingXingFBAInventoryProvider()

    # 创建抓取配置
    config = FetchConfig(
            data_provider=provider,
            users=finally_params.get('user'),
            task_params=save_params,
            fetch_params={
                'is_hide_zero_stock': finally_params.get('is_hide_zero_stock', 1)
            }
    )

    # 创建抓取器并执行
    fetcher = DataFetcher(config)
    results = fetcher.fetch_data()

    # 处理抓取结果
    # 初始化数据
    init_lingxing_fba_inventory()
    if results.get('data'):
        save_params.update({
            'task_num': len(results['data']),
            'platform_num': len(results['data']),
            'done_time': now_int()
        })
        # 插入数据库
        insert_lingxing_data(results['data'], save_params, provider.table_name)

        # 数据验证：检查是否存在total=0且history_total>0的异常数据
        validation_result = validate_fba_inventory_data()

        # 如果发现异常数据，进行修复
        if validation_result.get('has_anomaly') and validation_result.get('anomaly_records'):
            logging(f'发现 {validation_result["anomaly_count"]} 条异常数据，开始进行修复...')

            # 执行数据修复
            repair_result = repair_fba_inventory_data(
                validation_result['anomaly_records'],
                users=finally_params.get('user', ['jszg01', 'yxyJS2'])
            )

            logging(f'数据修复结果：{repair_result["message"]}')

            # 修复完成后再次验证
            if repair_result['success_count'] > 0:
                logging('修复完成，重新验证数据...')
                final_validation = validate_fba_inventory_data()
                logging(f'最终验证结果：{final_validation["message"]}')
        else:
            logging('数据验证通过，无需修复')


    # 处理失败页面
    print_results(results)

    return results


def run_fetch_lingxing_data_v2_gt_0(data_params):
    """抓取库存>0的数据"""
    pin = Pinner(True, True)
    main_fetch_lingxing_data_v2(data_params)
    pin.pin('库存>0抓取任务结束')


def run_fetch_lingxing_data_v2_all(data_params):
    """抓取全部库存数据"""
    pin = Pinner(True, True)
    main_fetch_lingxing_data_v2(data_params)
    pin.pin('全量库存抓取任务结束')


user = ['jszg01', 'yxyJS2']


def main():
    """
    1. 运行时每2小时抓取一遍库存>0并且立刻抓取一遍
    2. 开启定时任务，每天凌晨x点抓取全部
    3. 抓有库存的需要错开抓全部的时间段
    """
    # 设置每天凌晨x点抓取全部数据
    fetch_all_hour = 3
    is_full_fetch_running = False  # 添加全量抓取状态标志

    def run_full_fetch():
        nonlocal is_full_fetch_running
        try:
            is_full_fetch_running = True
            logging('开始执行全量库存抓取任务')
            run_fetch_lingxing_data_v2_all({
                'user': user,
                'is_hide_zero_stock': 0
            })
            logging('全量库存抓取任务完成')
        finally:
            is_full_fetch_running = False

    schedule.every().day.at(f"{fetch_all_hour:02d}:00").do(run_full_fetch)
    logging(f'已设置定时任务，每天 {fetch_all_hour:02d}:00 抓取全部数据')

    for hour in range(0, 24, 2):
        if abs(hour - fetch_all_hour) < 2:
            continue
        schedule.every().day.at(f"{hour:02d}:00").do(run_fetch_lingxing_data_v2_gt_0, {'user': user, 'is_hide_zero_stock': 1})
        logging(f'已设置定时任务，每天 {hour:02d}:00 抓取库存>0数据')

    schedule.run_pending()
    logging(f'开始执行库存>0抓取任务')
    run_fetch_lingxing_data_v2_gt_0({
        'user': user,
        'is_hide_zero_stock': 1
    })

    while True:
        try:
            # 检查是否有定时任务需要执行
            schedule.run_pending()

        except Exception as e:
            logging(f"发生错误: {str(e)}")
            traceback.print_exc()

        # 短暂休眠以避免过度占用CPU
        time.sleep(5)


if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    main()
