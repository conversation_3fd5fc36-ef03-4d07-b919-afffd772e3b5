# -*- coding:UTF-8 -*-
# @FileName  :测试数据验证和修复.py
# @Time      :2025/6/26 
# <AUTHOR>

"""
测试脚本：验证FBA库存数据验证和修复功能
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from utils_mrc.pub_fun import *
from 自动化.领星抓取.LingXingSessionPage import validate_fba_inventory_data, repair_fba_inventory_data
from utils_mrc.MysqlHelper import MysqlHelper

# 初始化数据库连接
MS = MysqlHelper()


def create_test_anomaly_data():
    """
    创建测试用的异常数据
    将一些记录的total设置为0，但保持history_total>0
    """
    try:
        # 查找一些有库存的记录用于测试
        test_sql = '''
            SELECT unique_id, fnsku, total 
            FROM idc.lingxing_fba_inventory 
            WHERE total > 0 
            LIMIT 5
        '''
        test_records = MS.get_dict(test_sql)
        
        if not test_records:
            logging('没有找到可用于测试的记录')
            return []
        
        # 将这些记录的total设置为0，创建异常数据
        test_unique_ids = [record['unique_id'] for record in test_records]
        
        # 先备份原始数据到history_total
        backup_sql = '''
            UPDATE idc.lingxing_fba_inventory 
            SET history_total = total 
            WHERE unique_id IN %s
        '''
        MS.update(backup_sql, (test_unique_ids,))
        
        # 将total设置为0，创建异常
        create_anomaly_sql = '''
            UPDATE idc.lingxing_fba_inventory 
            SET total = 0 
            WHERE unique_id IN %s
        '''
        result = MS.update(create_anomaly_sql, (test_unique_ids,))
        
        if result:
            logging(f'✅ 成功创建 {len(test_unique_ids)} 条测试异常数据')
            for record in test_records:
                logging(f'   - unique_id: {record["unique_id"]}, fnsku: {record["fnsku"]}')
            return test_unique_ids
        else:
            logging('❌ 创建测试异常数据失败')
            return []
            
    except Exception as e:
        logging(f'❌ 创建测试异常数据时发生错误: {str(e)}')
        return []


def restore_test_data(test_unique_ids):
    """
    恢复测试数据到原始状态
    """
    if not test_unique_ids:
        return
        
    try:
        restore_sql = '''
            UPDATE idc.lingxing_fba_inventory 
            SET total = history_total 
            WHERE unique_id IN %s
        '''
        result = MS.update(restore_sql, (test_unique_ids,))
        
        if result:
            logging(f'✅ 成功恢复 {len(test_unique_ids)} 条测试数据')
        else:
            logging('❌ 恢复测试数据失败')
            
    except Exception as e:
        logging(f'❌ 恢复测试数据时发生错误: {str(e)}')


def test_validation_and_repair():
    """
    测试数据验证和修复功能的完整流程
    """
    logging('🧪 开始测试数据验证和修复功能...')
    
    # 步骤1: 创建测试异常数据
    logging('\n📝 步骤1: 创建测试异常数据')
    test_unique_ids = create_test_anomaly_data()
    
    if not test_unique_ids:
        logging('❌ 无法创建测试数据，测试终止')
        return
    
    try:
        # 步骤2: 验证异常数据检测
        logging('\n🔍 步骤2: 验证异常数据检测')
        validation_result = validate_fba_inventory_data()
        
        logging(f'验证结果: {validation_result["message"]}')
        
        if not validation_result.get('has_anomaly'):
            logging('❌ 未检测到异常数据，验证功能可能有问题')
            return
        
        logging(f'✅ 成功检测到 {validation_result["anomaly_count"]} 条异常数据')
        
        # 显示异常记录详情
        if validation_result.get('anomaly_records'):
            logging('异常记录详情:')
            for i, record in enumerate(validation_result['anomaly_records'][:3]):  # 只显示前3条
                logging(f'   {i+1}. unique_id: {record["unique_id"]}, fnsku: {record["fnsku"]}, total: {record["total"]}, history_total: {record["history_total"]}')
        
        # 步骤3: 测试数据修复
        logging('\n🔧 步骤3: 测试数据修复')
        repair_result = repair_fba_inventory_data(
            validation_result['anomaly_records'], 
            users=['jszg01', 'yxyJS2']
        )
        
        logging(f'修复结果: {repair_result["message"]}')
        
        if repair_result['success_count'] > 0:
            logging(f'✅ 成功修复 {repair_result["success_count"]} 条记录')
        
        if repair_result['failed_count'] > 0:
            logging(f'⚠️ 修复失败 {repair_result["failed_count"]} 条记录')
            for failed_record in repair_result['failed_records'][:3]:  # 只显示前3条失败记录
                logging(f'   - unique_id: {failed_record["unique_id"]}, 原因: {failed_record["reason"]}')
        
        # 步骤4: 验证修复效果
        logging('\n✅ 步骤4: 验证修复效果')
        final_validation = validate_fba_inventory_data()
        logging(f'最终验证结果: {final_validation["message"]}')
        
        if final_validation['anomaly_count'] < validation_result['anomaly_count']:
            logging(f'🎉 修复有效！异常数据从 {validation_result["anomaly_count"]} 条减少到 {final_validation["anomaly_count"]} 条')
        else:
            logging('⚠️ 修复效果不明显，可能需要检查修复逻辑')
            
    finally:
        # 步骤5: 清理测试数据
        logging('\n🧹 步骤5: 清理测试数据')
        restore_test_data(test_unique_ids)
        
    logging('\n🏁 测试完成！')


def test_validation_only():
    """
    仅测试数据验证功能（不创建测试数据）
    """
    logging('🔍 测试当前数据库中的异常数据检测...')
    
    validation_result = validate_fba_inventory_data()
    logging(f'验证结果: {validation_result["message"]}')
    
    if validation_result.get('has_anomaly'):
        logging(f'⚠️ 发现 {validation_result["anomaly_count"]} 条异常数据')
        
        # 显示前5条异常记录
        if validation_result.get('anomaly_records'):
            logging('异常记录详情（前5条）:')
            for i, record in enumerate(validation_result['anomaly_records'][:5]):
                logging(f'   {i+1}. unique_id: {record["unique_id"]}, fnsku: {record["fnsku"]}, '
                       f'asin: {record["asin"]}, total: {record["total"]}, history_total: {record["history_total"]}')
    else:
        logging('✅ 数据正常，未发现异常记录')


if __name__ == '__main__':
    logging(f'开始运行数据验证和修复测试脚本...')
    
    # 选择测试模式
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == 'full':
        # 完整测试（包括创建测试数据）
        test_validation_and_repair()
    else:
        # 仅验证当前数据
        test_validation_only()
        
    logging('测试脚本执行完毕')
